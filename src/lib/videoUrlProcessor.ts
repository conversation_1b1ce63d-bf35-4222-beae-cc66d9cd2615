/**
 * Video URL Processing Utilities
 * Handles downloading and processing videos from various social media platforms
 */

interface VideoInfo {
  buffer: Buffer;
  mimeType: string;
  platform: string;
  originalUrl: string;
}

/**
 * Identifies the platform from a video URL
 */
export function identifyPlatform(url: string): string {
  if (url.includes('tiktok.com')) return 'TikTok';
  if (url.includes('instagram.com')) return 'Instagram';
  if (url.includes('youtube.com') || url.includes('youtu.be')) return 'YouTube';
  if (url.includes('twitter.com') || url.includes('x.com')) return 'Twitter/X';
  if (url.includes('facebook.com')) return 'Facebook';
  return 'Unknown';
}

/**
 * Validates if a URL is from a supported platform
 */
export function isSupportedPlatform(url: string): boolean {
  const supportedDomains = [
    'tiktok.com',
    'instagram.com',
    'youtube.com',
    'youtu.be',
    'twitter.com',
    'x.com',
    'facebook.com'
  ];
  
  return supportedDomains.some(domain => url.includes(domain));
}

/**
 * Downloads video from URL using third-party API services
 */
export async function downloadVideoFromUrl(url: string): Promise<VideoInfo> {
  const platform = identifyPlatform(url);

  if (!isSupportedPlatform(url)) {
    throw new Error(`Unsupported platform. Supported platforms: TikTok, Instagram, YouTube, Twitter/X, Facebook`);
  }

  try {
    // Use our download API service
    const response = await fetch('/api/download-video', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url, platform })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to download video from ${platform}`);
    }

    const data = await response.json();

    if (!data.success || !data.videoData) {
      throw new Error(`No video data received from ${platform}`);
    }

    // Convert base64 back to buffer
    const buffer = Buffer.from(data.videoData, 'base64');

    return {
      buffer,
      mimeType: data.mimeType || 'video/mp4',
      platform,
      originalUrl: url
    };
  } catch (error) {
    throw new Error(`Failed to process video from ${platform}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extracts video metadata from URL (without downloading)
 * This can be used for basic validation and info display
 */
export async function getVideoMetadata(url: string): Promise<{
  platform: string;
  isValid: boolean;
  title?: string;
  duration?: number;
  thumbnail?: string;
}> {
  const platform = identifyPlatform(url);
  
  if (!isSupportedPlatform(url)) {
    return {
      platform,
      isValid: false
    };
  }

  // Basic URL validation
  try {
    new URL(url);
  } catch {
    return {
      platform,
      isValid: false
    };
  }

  // For now, return basic info
  // In production, you might use APIs or services to get metadata
  return {
    platform,
    isValid: true,
    title: `Video from ${platform}`,
    duration: undefined, // Would be fetched from API
    thumbnail: undefined // Would be fetched from API
  };
}

/**
 * Converts various video formats to MP4 for Gemini compatibility
 */
export async function convertToMp4(buffer: Buffer, originalMimeType: string): Promise<Buffer> {
  // This would use ffmpeg or similar to convert video formats
  // For now, assume the video is already in a compatible format
  
  if (originalMimeType === 'video/mp4') {
    return buffer;
  }
  
  // In production, you would use ffmpeg here:
  // const ffmpeg = require('fluent-ffmpeg');
  // ... conversion logic
  
  throw new Error(`Video format conversion from ${originalMimeType} to MP4 is not yet implemented.`);
}

/**
 * Validates video size and format for Gemini API compatibility
 */
export function validateVideoForGemini(buffer: Buffer, mimeType: string): {
  isValid: boolean;
  error?: string;
} {
  const maxSize = 20 * 1024 * 1024; // 20MB limit for Gemini
  const supportedTypes = ['video/mp4'];
  
  if (buffer.length > maxSize) {
    return {
      isValid: false,
      error: `Video is too large (${(buffer.length / 1024 / 1024).toFixed(1)}MB). Maximum size is 20MB.`
    };
  }
  
  if (!supportedTypes.includes(mimeType)) {
    return {
      isValid: false,
      error: `Unsupported video format: ${mimeType}. Supported formats: ${supportedTypes.join(', ')}`
    };
  }
  
  return { isValid: true };
}
