@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Markdown content readability */
.prose p {
  margin-bottom: 1em; /* Add space below paragraphs */
}

.prose ul,
.prose ol {
  margin-bottom: 1em; /* Add space below lists */
  padding-left: 1.5em; /* Indent list items */
}

.prose li {
  margin-bottom: 0.5em; /* Space between list items */
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  margin-top: 1.5em; /* Space above headings */
  margin-bottom: 1em; /* Space below headings */
}

.prose pre {
  margin-top: 1em;
  margin-bottom: 1em;
  padding: 1em;
  background-color: #f3f4f6; /* Light gray background for code blocks */
  border-radius: 0.375rem; /* Rounded corners */
  overflow-x: auto; /* Enable horizontal scrolling for long lines */
}

.prose code {
  font-size: 0.875em; /* Slightly smaller font for inline code */
  background-color: #e5e7eb; /* Light gray background for inline code */
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
}
