"use client";

import React, { useState, useEffect } from 'react';
import AIChatbox from '@/components/AIChatbox';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const LinerBridgePage = () => {
  const [businessName, setBusinessName] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [businessDescription, setBusinessDescription] = useState('');
  const [marketingMessages, setMarketingMessages] = useState('');
  const [oneLinerHeadlines, setOneLinerHeadlines] = useState<string[]>([]); // New state for one-liner headlines
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [shouldSendForgetPrompt, setShouldSendForgetPrompt] = useState(false); // New state for LLM reset

  // Load all data from localStorage on initial load
  useEffect(() => {
    const savedBusinessName = localStorage.getItem('linerBridgeBusinessName');
    if (savedBusinessName) {
      setBusinessName(savedBusinessName);
    }
    const savedTargetAudience = localStorage.getItem('linerBridgeTargetAudience');
    if (savedTargetAudience) {
      setTargetAudience(savedTargetAudience);
    }
    const savedBusinessDescription = localStorage.getItem('linerBridgeBusinessDescription');
    if (savedBusinessDescription) {
      setBusinessDescription(savedBusinessDescription);
    }
    const savedMarketingMessages = localStorage.getItem('linerBridgeMarketingMessages');
    if (savedMarketingMessages) {
      setMarketingMessages(savedMarketingMessages);
    }
    const savedOneLinerHeadlines = localStorage.getItem('linerBridgeOneLinerHeadlines');
    if (savedOneLinerHeadlines) {
      setOneLinerHeadlines(JSON.parse(savedOneLinerHeadlines));
    }
    const savedChatMessages = localStorage.getItem('linerBridgeChatMessages');
    if (savedChatMessages) {
      setChatMessages(JSON.parse(savedChatMessages));
    }
  }, []);

  // Save all data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('linerBridgeBusinessName', businessName);
  }, [businessName]);

  useEffect(() => {
    localStorage.setItem('linerBridgeTargetAudience', targetAudience);
  }, [targetAudience]);

  useEffect(() => {
    localStorage.setItem('linerBridgeBusinessDescription', businessDescription);
  }, [businessDescription]);

  useEffect(() => {
    localStorage.setItem('linerBridgeMarketingMessages', marketingMessages);
  }, [marketingMessages]);

  useEffect(() => {
    localStorage.setItem('linerBridgeOneLinerHeadlines', JSON.stringify(oneLinerHeadlines));
  }, [oneLinerHeadlines]);

  useEffect(() => {
    localStorage.setItem('linerBridgeChatMessages', JSON.stringify(chatMessages));
  }, [chatMessages]);

  const handleGenerate = async () => {
    setLoading(true);
    setError('');
    setMarketingMessages(''); // Clear previous result
    setOneLinerHeadlines([]); // Clear previous one-liner headlines
    setChatMessages([]); // Clear previous chat messages

    let messagesToSend = [];
    if (shouldSendForgetPrompt) {
      messagesToSend.push({ role: "system", content: "User has initiated a full reset. Disregard all previous conversation history and start fresh." });
      setShouldSendForgetPrompt(false); // Reset the flag after preparing the message
    }

    try {
      const response = await fetch('/api/generate-marketing-messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ businessName, targetAudience, businessDescription, messages: messagesToSend, generateOneLinerHeadlines: true }), // Pass messagesToSend and new flag to backend
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Something went wrong with the generation.');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get reader for response body.');
      }

      const decoder = new TextDecoder();
      let fullResponseContent = ''; // Accumulate full response here

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        fullResponseContent += decoder.decode(value, { stream: true });
        // Optionally, update a temporary state for streaming display if needed, but not marketingMessages directly
        // For now, we'll just accumulate and parse at the end
      }

      // Once streaming is complete, parse the received text into different sections
      const oneLinerRegex = /## 50 One-Liner Headlines:\n([\s\S]*?)(?=## 50 Website Headlines:|$)/;
      const websiteHeadlinesRegex = /## 50 Website Headlines:\n([\s\S]*?)(?=## 50 Sub-Headlines:|$)/;
      const subHeadlinesRegex = /## 50 Sub-Headlines:\n([\s\S]*)/;

      // Re-evaluate the order of regex matches based on the new prompt structure
      const oneLinerMatch = fullResponseContent.match(oneLinerRegex);
      const websiteHeadlinesMatch = fullResponseContent.match(websiteHeadlinesRegex);
      const subHeadlinesMatch = fullResponseContent.match(subHeadlinesRegex);

      let parsedOneLinerHeadlines: string[] = [];
      if (oneLinerMatch && oneLinerMatch[1]) {
        parsedOneLinerHeadlines = oneLinerMatch[1].split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0 && !line.startsWith('*')); // Filter out empty lines and markdown list markers
      }
      setOneLinerHeadlines(parsedOneLinerHeadlines);

      let parsedMarketingMessages = '';
      if (websiteHeadlinesMatch && websiteHeadlinesMatch[1]) {
        parsedMarketingMessages += '## 50 Website Headlines:\n' + websiteHeadlinesMatch[1];
      }
      if (subHeadlinesMatch && subHeadlinesMatch[1]) {
        parsedMarketingMessages += '\n## 50 Sub-Headlines:\n' + subHeadlinesMatch[1];
      }
      setMarketingMessages(parsedMarketingMessages);

      // Add the final analysis result to chatMessages, using the full response content
      setChatMessages([{ role: "assistant", content: fullResponseContent }]);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      setBusinessName('');
      setTargetAudience('');
      setBusinessDescription('');
      setMarketingMessages('');
      setOneLinerHeadlines([]); // Clear one-liner headlines on clear
      setChatMessages([]);
      localStorage.removeItem('linerBridgeBusinessName');
      localStorage.removeItem('linerBridgeTargetAudience');
      localStorage.removeItem('linerBridgeBusinessDescription');
      localStorage.removeItem('linerBridgeMarketingMessages');
      localStorage.removeItem('linerBridgeOneLinerHeadlines'); // Clear one-liner headlines from local storage
      localStorage.removeItem('linerBridgeChatMessages');
      setShouldSendForgetPrompt(true); // Set flag to send forget prompt on next LLM call
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">1 Liner Bridge</h1>
      <p className="mb-6">Generate compelling marketing messages for your product.</p>


      <div className="mb-4">
        <label htmlFor="businessName" className="block text-sm font-medium text-gray-700">
          Business Name
        </label>
        <input
          type="text"
          id="businessName"
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={businessName}
          onChange={(e) => setBusinessName(e.target.value)}
          placeholder='Enter your business name'
        />
      </div>

      <div className="mb-6">
        <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700">
          Target Audience
        </label>
        <textarea
          id="targetAudience"
          rows={5}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={targetAudience}
          onChange={(e) => setTargetAudience(e.target.value)}
          placeholder='Describe your target audience in detail'
        ></textarea>
      </div>

      <div className="mb-6">
        <label htmlFor="businessDescription" className="block text-sm font-medium text-gray-700">
          Business Description
        </label>
        <textarea
          id="businessDescription"
          rows={5}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={businessDescription}
          onChange={(e) => setBusinessDescription(e.target.value)}
          placeholder='Describe your business in detail'
        ></textarea>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={handleGenerate}
          disabled={loading || !businessName || !targetAudience || !businessDescription}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Generating...' : 'Generate Marketing Messages'}
        </button>
        <button
          onClick={handleClear}
          className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Clear All
        </button>
      </div>

      {error && (
        <div className="mt-4 text-red-600">
          Error: {error}
        </div>
      )}

      {chatMessages.length > 0 && (
        <div className="mt-6">
          {oneLinerHeadlines.length > 0 && (
            <div className="mb-6 p-4 border border-gray-200 rounded-md bg-gray-50">
              <h2 className="text-xl font-semibold mb-2">One-Liner Headlines:</h2>
              <div className="space-y-1">
                {oneLinerHeadlines.map((headline, index) => (
                  <p key={index} className="text-gray-800">{headline}</p>
                ))}
              </div>
            </div>
          )}
          {marketingMessages && (
            <div className="p-4 border border-gray-200 rounded-md bg-gray-50 mb-6">
              <h2 className="text-xl font-semibold mb-2">2-Pronged Website Headlines and Sub-Headlines:</h2>
              <div className="prose prose-indigo max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>{marketingMessages}</ReactMarkdown>
              </div>
            </div>
          )}
          <h2 className="text-xl font-semibold mb-4">AI Chatbox for Follow-up:</h2>
          <AIChatbox
            initialMessage={"Here are the generated marketing messages:\n\n" + marketingMessages}
            messages={chatMessages}
            setMessages={setChatMessages}
            shouldSendForgetPrompt={shouldSendForgetPrompt}
            resetForgetPrompt={() => setShouldSendForgetPrompt(false)}
          />
        </div>
      )}
    </div>
  );
};

export default LinerBridgePage;
