"use client";

import React, { useState, useEffect } from 'react';
import AIChatbox from '@/components/AIChatbox';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const AppReviewsPage = () => {
  const [appReviews, setAppReviews] = useState('');
  const [analysisResult, setAnalysisResult] = useState('');
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [shouldSendForgetPrompt, setShouldSendForgetPrompt] = useState(false); // New state for LLM reset

  // Load all data from localStorage on initial load
  useEffect(() => {
    const savedAppReviews = localStorage.getItem('appReviews');
    if (savedAppReviews) {
      setAppReviews(savedAppReviews);
    }
    const savedAnalysisResult = localStorage.getItem('appReviewsAnalysisResult');
    if (savedAnalysisResult) {
      setAnalysisResult(savedAnalysisResult);
    }
    const savedChatMessages = localStorage.getItem('appReviewsChatMessages');
    if (savedChatMessages) {
      setChatMessages(JSON.parse(savedChatMessages));
    }
  }, []);

  // Save all data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('appReviews', appReviews);
  }, [appReviews]);

  useEffect(() => {
    localStorage.setItem('appReviewsAnalysisResult', analysisResult);
  }, [analysisResult]);

  useEffect(() => {
    localStorage.setItem('appReviewsChatMessages', JSON.stringify(chatMessages));
  }, [chatMessages]);

  const handleAnalyze = async () => {
    setLoading(true);
    setError('');
    setAnalysisResult(''); // Clear previous result
    setChatMessages([]); // Clear previous chat messages

    let messagesToSend = [];
    if (shouldSendForgetPrompt) {
      messagesToSend.push({ role: "system", content: "User has initiated a full reset. Disregard all previous conversation history and start fresh." });
      setShouldSendForgetPrompt(false); // Reset the flag after preparing the message
    }

    try {
      const response = await fetch('/api/analyze-reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ appReviews, messages: messagesToSend }), // Pass messagesToSend to backend
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Something went wrong with the analysis.');
      }

      const data = await response.json();
      setAnalysisResult(data.result);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      setAppReviews('');
      setAnalysisResult('');
      setChatMessages([]);
      localStorage.removeItem('appReviews');
      localStorage.removeItem('appReviewsAnalysisResult');
      localStorage.removeItem('appReviewsChatMessages');
      setShouldSendForgetPrompt(true); // Set flag to send forget prompt on next LLM call
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">App Store Reviews Analysis</h1>
      <p className="mb-6">Analyze app store reviews to gain insights and recommendations.</p>

      <div className="mb-6">
        <label htmlFor="appReviews" className="block text-sm font-medium text-gray-700">
          App Reviews (Paste reviews here, one per line or separated by newlines)
        </label>
        <textarea
          id="appReviews"
          rows={10}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={appReviews}
          onChange={(e) => setAppReviews(e.target.value)}
          placeholder='Paste your app reviews here'
        ></textarea>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={handleAnalyze}
          disabled={loading || !appReviews}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Analyzing...' : 'Analyze Reviews'}
        </button>
        <button
          onClick={handleClear}
          className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Clear All
        </button>
      </div>

      {error && (
        <div className="mt-4 text-red-600">
          Error: {error}
        </div>
      )}

      {analysisResult && (
        <div className="mt-6">
          <div className="p-4 border border-gray-200 rounded-md bg-gray-50 mb-6">
            <h2 className="text-xl font-semibold mb-2">Analysis Result:</h2>
            <div className="prose prose-indigo max-w-none">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{analysisResult}</ReactMarkdown>
            </div>
          </div>
          <h2 className="text-xl font-semibold mb-4">AI Chatbox for Follow-up:</h2>
          <AIChatbox
            initialMessage={"Here is the analysis of your app reviews:\n\n" + analysisResult}
            messages={chatMessages}
            setMessages={setChatMessages}
            shouldSendForgetPrompt={shouldSendForgetPrompt}
            resetForgetPrompt={() => setShouldSendForgetPrompt(false)}
          />
        </div>
      )}
    </div>
  );
};

export default AppReviewsPage;
