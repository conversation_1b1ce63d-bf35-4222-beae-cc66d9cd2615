"use client";

import React, { useState, useEffect } from 'react';
import AIChatbox from '@/components/AIChatbox';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const LeiJunPage = () => {
  const [businessIdeal, setBusinessIdeal] = useState('');
  const [description, setDescription] = useState('');
  const [analysisResult, setAnalysisResult] = useState('');
  const [chatMessages, setChatMessages] = useState<any[]>([]); // State for chat messages
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [shouldSendForgetPrompt, setShouldSendForgetPrompt] = useState(false); // New state for LLM reset

  // Load all data from localStorage on initial load
  useEffect(() => {
    const savedBusinessIdeal = localStorage.getItem('leiJunBusinessIdeal');
    if (savedBusinessIdeal) {
      setBusinessIdeal(savedBusinessIdeal);
    }
    const savedDescription = localStorage.getItem('leiJunDescription');
    if (savedDescription) {
      setDescription(savedDescription);
    }
    const savedAnalysisResult = localStorage.getItem('leiJunAnalysisResult');
    if (savedAnalysisResult) {
      setAnalysisResult(savedAnalysisResult);
    }
    const savedChatMessages = localStorage.getItem('leiJunChatMessages');
    if (savedChatMessages) {
      setChatMessages(JSON.parse(savedChatMessages));
    }
  }, []);

  // Save all data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('leiJunBusinessIdeal', businessIdeal);
  }, [businessIdeal]);

  useEffect(() => {
    localStorage.setItem('leiJunDescription', description);
  }, [description]);

  useEffect(() => {
    localStorage.setItem('leiJunAnalysisResult', analysisResult);
  }, [analysisResult]);

  useEffect(() => {
    localStorage.setItem('leiJunChatMessages', JSON.stringify(chatMessages));
  }, [chatMessages]);

  const handleAnalyze = async () => {
    setLoading(true);
    setError('');
    setAnalysisResult(''); // Clear previous analysis result
    setChatMessages([]); // Clear previous chat messages

    let messagesToSend = [];
    if (shouldSendForgetPrompt) {
      messagesToSend.push({ role: "system", content: "User has initiated a full reset. Disregard all previous conversation history and start fresh." });
      setShouldSendForgetPrompt(false); // Reset the flag after preparing the message
    }
    // Add the current user prompt to messagesToSend if needed for the initial analysis
    // For analyze-ideal, the prompt is constructed on the backend, so we don't need to send chatMessages here.
    // The "forget" prompt is a meta-instruction for the LLM's internal state.

    try {
      const response = await fetch('/api/analyze-ideal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ businessIdeal, description, messages: messagesToSend }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Something went wrong with the analysis.');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get reader for response body.');
      }

      const decoder = new TextDecoder();
      let receivedText = '';
      setAnalysisResult(''); // Clear previous result to start appending

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        receivedText += decoder.decode(value, { stream: true });
        setAnalysisResult(receivedText); // Update state with each chunk
      }
      // Once streaming is complete, add the final analysis result to chatMessages
      setChatMessages([{ role: "assistant", content: receivedText }]);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      setBusinessIdeal('');
      setDescription('');
      setAnalysisResult('');
      setChatMessages([]);
      localStorage.removeItem('leiJunBusinessIdeal');
      localStorage.removeItem('leiJunDescription');
      localStorage.removeItem('leiJunAnalysisResult');
      localStorage.removeItem('leiJunChatMessages');
      setShouldSendForgetPrompt(true); // Set flag to send forget prompt on next LLM call
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Lei Jun Ebook Methodology</h1>
      <p className="mb-6">This page helps you analyze and improve your business ideals using the Lei Jun methodology.</p>

      <div className="mb-4">

          <label htmlFor="businessIdeal" className="block text-sm font-medium text-gray-700">
  Your Business Ideal (e.g., {"\"Math Solver app\""})
</label>
        <input
          type="text"
          id="businessIdeal"
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={businessIdeal}
          onChange={(e) => setBusinessIdeal(e.target.value)}
          placeholder='Enter your business ideal'
        />
      </div>

      <div className="mb-6">
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description (provide more details)
        </label>
        <textarea
          id="description"
          rows={5}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder='Describe your business ideal in more detail'
        ></textarea>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={handleAnalyze}
          disabled={loading || !businessIdeal || !description}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Analyzing...' : 'Analyze'}
        </button>
        <button
          onClick={handleClear}
          className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Clear All
        </button>
      </div>

      {error && (
        <div className="mt-4 text-red-600">
          Error: {error}
        </div>
      )}

      {analysisResult && (
        <div className="mt-6">
          <div className="p-4 border border-gray-200 rounded-md bg-gray-50 mb-6">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-xl font-semibold">Analysis Result:</h2>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(analysisResult);
                  alert('Analysis result copied to clipboard!');
                }}
                className="ml-4 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Copy Markdown
              </button>
            </div>
            <div className="prose prose-indigo max-w-none">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{analysisResult}</ReactMarkdown>
            </div>
          </div>
          <h2 className="text-xl font-semibold mb-4">AI Chatbox for Follow-up:</h2>
          <AIChatbox
            initialMessage={"Here is the analysis for your business ideal:\n\n" + analysisResult}
            messages={chatMessages}
            setMessages={setChatMessages}
            shouldSendForgetPrompt={shouldSendForgetPrompt}
            resetForgetPrompt={() => setShouldSendForgetPrompt(false)}
          />
        </div>
      )}
    </div>
  );
};

export default LeiJunPage;
