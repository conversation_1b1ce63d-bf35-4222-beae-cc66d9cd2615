'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';

interface BehavioralInsight {
  title: string;
  principle: string;
  application: string;
  implementation: string;
  examples: string[];
}

interface GeneratedContent {
  insights: BehavioralInsight[];
}

interface StreamingBehavioralInsight extends BehavioralInsight {
  isComplete: boolean;
}

const parseStreamingInsights = (text: string): StreamingBehavioralInsight[] => {
  const insights: StreamingBehavioralInsight[] = [];
  
  // Look for JSON structure in the streaming text
  const jsonMatch = text.match(/\{[\s\S]*"behavioral_insights"[\s\S]*\}/);
  if (!jsonMatch) return insights;
  
  try {
    const parsed = JSON.parse(jsonMatch[0]);
    if (parsed.behavioral_insights && Array.isArray(parsed.behavioral_insights)) {
      return parsed.behavioral_insights.map((insight: any) => ({
        title: insight.title || 'Loading...',
        principle: insight.principle || 'Loading...',
        application: insight.application || 'Loading...',
        implementation: insight.implementation || 'Loading...',
        examples: insight.examples || [],
        isComplete: !!(insight.title && insight.principle && insight.application && insight.implementation)
      }));
    }
  } catch (e) {
    // If JSON parsing fails, try to extract partial insights
    const insightPattern = /"title":\s*"([^"]*)"[\s\S]*?"principle":\s*"([^"]*)"[\s\S]*?"application":\s*"([^"]*)"[\s\S]*?"implementation":\s*"([^"]*)"/g;
    
    let match;
    while ((match = insightPattern.exec(text)) !== null) {
      const [, title, principle, application, implementation] = match;
      
      insights.push({
        title: title || 'Loading...',
        principle: principle || 'Loading...',
        application: application || 'Loading...',
        implementation: implementation || 'Loading...',
        examples: [],
        isComplete: !!(title && principle && application && implementation)
      });
    }
  }
  
  return insights;
};

const NancyHarhut2Page = () => {
  const [productName, setProductName] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [callToAction, setCallToAction] = useState('');
  const [behavioralFocus, setBehavioralFocus] = useState('');
  const [includePrincipleDetails, setIncludePrincipleDetails] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [rawStreamContent, setRawStreamContent] = useState('');
  const [streamingInsights, setStreamingInsights] = useState<StreamingBehavioralInsight[]>([]);

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedProductName = localStorage.getItem('nancyharhut2ProductName');
    const savedProductDescription = localStorage.getItem('nancyharhut2ProductDescription');
    const savedTargetAudience = localStorage.getItem('nancyharhut2TargetAudience');
    const savedCallToAction = localStorage.getItem('nancyharhut2CallToAction');
    const savedBehavioralFocus = localStorage.getItem('nancyharhut2BehavioralFocus');
    const savedGeneratedContent = localStorage.getItem('nancyharhut2GeneratedContent');

    if (savedProductName) setProductName(savedProductName);
    if (savedProductDescription) setProductDescription(savedProductDescription);
    if (savedTargetAudience) setTargetAudience(savedTargetAudience);
    if (savedCallToAction) setCallToAction(savedCallToAction);
    if (savedBehavioralFocus) setBehavioralFocus(savedBehavioralFocus);
    if (savedGeneratedContent) {
      try {
        setGeneratedContent(JSON.parse(savedGeneratedContent));
      } catch (e) {
        console.error('Failed to parse saved content:', e);
      }
    }
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem('nancyharhut2ProductName', productName);
  }, [productName]);

  useEffect(() => {
    localStorage.setItem('nancyharhut2ProductDescription', productDescription);
  }, [productDescription]);

  useEffect(() => {
    localStorage.setItem('nancyharhut2TargetAudience', targetAudience);
  }, [targetAudience]);

  useEffect(() => {
    localStorage.setItem('nancyharhut2CallToAction', callToAction);
  }, [callToAction]);

  useEffect(() => {
    localStorage.setItem('nancyharhut2BehavioralFocus', behavioralFocus);
  }, [behavioralFocus]);

  useEffect(() => {
    if (generatedContent) {
      localStorage.setItem('nancyharhut2GeneratedContent', JSON.stringify(generatedContent));
    }
  }, [generatedContent]);

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      setProductName('');
      setProductDescription('');
      setTargetAudience('');
      setCallToAction('');
      setBehavioralFocus('');
      setGeneratedContent(null);
      setRawStreamContent('');
      setStreamingInsights([]);
      
      // Clear localStorage
      localStorage.removeItem('nancyharhut2ProductName');
      localStorage.removeItem('nancyharhut2ProductDescription');
      localStorage.removeItem('nancyharhut2TargetAudience');
      localStorage.removeItem('nancyharhut2CallToAction');
      localStorage.removeItem('nancyharhut2BehavioralFocus');
      localStorage.removeItem('nancyharhut2GeneratedContent');
    }
  };

  const handleGenerateInsights = async () => {
    if (!productName.trim() || !productDescription.trim() || !targetAudience.trim() || !callToAction.trim()) {
      setError('Please fill in all required fields.');
      return;
    }

    setLoading(true);
    setError('');
    setGeneratedContent(null);
    setRawStreamContent('');
    setStreamingInsights([]);

    try {
      const response = await fetch('/api/analyze-nancy-harhut-2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productName,
          productDescription,
          targetAudience,
          callToAction,
          behavioralFocus: behavioralFocus.trim() || undefined,
          includePrincipleDetails,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate behavioral insights.');
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let receivedText = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          receivedText += decoder.decode(value, { stream: true });
          setRawStreamContent(receivedText);
          
          // Parse streaming insights
          const insights = parseStreamingInsights(receivedText);
          setStreamingInsights(insights);
        }

        // Final parsing
        try {
          const finalInsights = parseStreamingInsights(receivedText);
          if (finalInsights.length > 0) {
            setGeneratedContent({ insights: finalInsights });
          }
        } catch (e) {
          console.error('Failed to parse final insights:', e);
        }
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-2">Nancy Harhut Behavioral Science Playbook</h1>
          <p className="text-gray-600">
            Generate targeted behavioral science insights for your product using Nancy Harhut's proven psychological principles.
            Get specific, actionable strategies that leverage cognitive biases and behavioral triggers to improve marketing effectiveness.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Cache Status Indicator */}
          {(productName || generatedContent || behavioralFocus) && (
            <div className="flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Data Cached
            </div>
          )}
          <button
            onClick={handleClear}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Clear All
          </button>
        </div>
      </div>

      {/* Product & Campaign Input Form */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">Tell Us About Your Product</h2>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product/Service Name *
                </label>
                <input
                  type="text"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g., FitTracker Pro, CloudSync Business, EcoClean Solutions"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Description *
                </label>
                <textarea
                  value={productDescription}
                  onChange={(e) => setProductDescription(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Describe your product's key features, benefits, and unique value proposition..."
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Audience *
                </label>
                <input
                  type="text"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g., Small business owners, Health-conscious millennials, B2B decision makers"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Call to Action *
                </label>
                <input
                  type="text"
                  value={callToAction}
                  onChange={(e) => setCallToAction(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g., Sign up for free trial, Schedule a demo, Buy now with 30% off"
                />
              </div>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Behavioral Science Focus Areas (Optional)
              </label>
              <textarea
                value={behavioralFocus}
                onChange={(e) => setBehavioralFocus(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="Specify which behavioral science principles to focus on: e.g., 'Loss aversion and scarcity for urgency', 'Social proof and authority for trust building', 'Reciprocity and consistency for conversion', etc."
              />
              <p className="text-xs text-gray-500 mt-1">
                Leave blank to get a comprehensive analysis across all Nancy Harhut principles, or specify focus areas for targeted insights.
              </p>

              <div className="bg-blue-50 rounded-lg p-4 mt-3">
                <h4 className="font-semibold text-blue-900 mb-2">Example Focus Areas:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• &quot;Loss aversion and scarcity to create urgency and FOMO&quot;</li>
                  <li>• &quot;Social proof and authority to build trust and credibility&quot;</li>
                  <li>• &quot;Reciprocity and consistency principles for conversion optimization&quot;</li>
                  <li>• &quot;Storytelling and emotional triggers for engagement&quot;</li>
                  <li>• &quot;Choice architecture and framing effects for decision-making&quot;</li>
                  <li>• &quot;Curiosity gaps and information asymmetry for attention&quot;</li>
                  <li>• &quot;Temporal landmarks and availability bias for timing&quot;</li>
                  <li>• &quot;Autonomy bias and labeling for customer empowerment&quot;</li>
                </ul>
              </div>
            </div>

            {/* Principle Details Checkbox */}
            <div className="md:col-span-2 mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  id="includePrincipleDetails"
                  checked={includePrincipleDetails}
                  onChange={(e) => setIncludePrincipleDetails(e.target.checked)}
                  className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <div className="ml-3">
                  <label htmlFor="includePrincipleDetails" className="text-sm font-medium text-yellow-800">
                    Include Detailed Principle Explanations
                  </label>
                  <p className="text-xs text-yellow-700 mt-1">
                    Check this to include detailed explanations of the behavioral science principles being applied, with psychological background and research context.
                  </p>
                </div>
              </div>
            </div>

            <div className="md:col-span-2 flex justify-center mt-6">
              <button
                onClick={handleGenerateInsights}
                disabled={loading || !productName.trim() || !productDescription.trim() || !targetAudience.trim() || !callToAction.trim()}
                className="px-8 py-3 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Generating Behavioral Insights...' : 'Generate Behavioral Science Playbook'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 border border-red-300 rounded-md bg-red-50">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Loading State with Streaming Content */}
      {loading && (
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mr-3"></div>
            <h2 className="text-2xl font-semibold text-gray-900">Generating Your Behavioral Science Playbook...</h2>
          </div>

          {rawStreamContent && (
            <div className="bg-gray-50 rounded-lg p-4 border">
              <div className="whitespace-pre-wrap text-sm text-gray-700">
                {rawStreamContent}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Results Display */}
      {generatedContent?.insights && (
        <div className="mb-8">
          <div className="flex items-center mb-6">
            <div className="bg-green-100 p-2 rounded-full mr-3">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">Your Behavioral Science Playbook</h2>
              <p className="text-gray-600 mb-2">
                {generatedContent.insights.length} behavioral science insights based on Nancy Harhut's principles
              </p>
              <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Powered by Nancy Harhut's Behavioral Science Framework
              </div>
            </div>
          </div>

          <div className="grid gap-6">
            {generatedContent.insights.map((insight, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white">{insight.title}</h3>
                    <span className="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">
                      Insight #{index + 1}
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <svg className="w-4 h-4 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Behavioral Principle
                      </h4>
                      <p className="text-gray-700 text-sm mb-4">{insight.principle}</p>

                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Application Strategy
                      </h4>
                      <p className="text-gray-700 text-sm">{insight.application}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <svg className="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Implementation Guide
                      </h4>
                      <p className="text-gray-700 text-sm mb-4">{insight.implementation}</p>

                      {insight.examples && insight.examples.length > 0 && (
                        <>
                          <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                            <svg className="w-4 h-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            Examples
                          </h4>
                          <ul className="text-gray-700 text-sm space-y-1">
                            {insight.examples.map((example, exampleIndex) => (
                              <li key={exampleIndex} className="flex items-start">
                                <span className="text-orange-500 mr-2">•</span>
                                {example}
                              </li>
                            ))}
                          </ul>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default NancyHarhut2Page;
