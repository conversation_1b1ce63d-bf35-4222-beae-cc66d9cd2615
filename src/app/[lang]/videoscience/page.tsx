'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';

interface ViralAnatomyElement {
  name: string;
  explanation: string;
  videoExample: string;
}

interface VideoAnalysis {
  fileName?: string;
  viralAnatomy: ViralAnatomyElement[];
}

interface VideoConcept {
  title: string;
  core_principle: string;
  scene_prompts: string[];
  audio_suggestion: string;
  cta_implementation: string;
  veo3_visual_specs: string[];
  veo3_audio_specs: string[];
}

interface GeneratedContent {
  videoAnalysis: VideoAnalysis;
  videoConcepts: VideoConcept[];
}

interface StreamingVideoConcept extends VideoConcept {
  isComplete: boolean;
}

// Helper function to extract partial video concepts from streaming JSON
const extractPartialConcepts = (jsonText: string): StreamingVideoConcept[] => {
  const concepts: StreamingVideoConcept[] = [];

  try {
    // Try to find individual concept objects in the streaming JSON
    // Look for complete concept objects first
    const conceptPattern = /\{[^{}]*?"title":\s*"([^"]*)"[^{}]*?"core_principle":\s*"([^"]*)"[^{}]*?"scene_prompts":\s*\[([^\]]*?)\][^{}]*?"audio_suggestion":\s*"([^"]*)"[^{}]*?"cta_implementation":\s*"([^"]*)"[^{}]*?\}/g;

    let match;
    while ((match = conceptPattern.exec(jsonText)) !== null) {
      const [, title, core_principle, scene_prompts_raw, audio_suggestion, cta_implementation] = match;

      // Parse scene prompts
      let scene_prompts: string[] = [];
      try {
        const promptMatches = scene_prompts_raw.match(/"([^"]*)"/g);
        scene_prompts = promptMatches ? promptMatches.map(p => p.slice(1, -1)) : [];
      } catch (e) {
        scene_prompts = [];
      }

      concepts.push({
        title: title || 'Loading...',
        core_principle: core_principle || 'Loading...',
        scene_prompts,
        audio_suggestion: audio_suggestion || 'Loading...',
        cta_implementation: cta_implementation || 'Loading...',
        veo3_visual_specs: [],
        veo3_audio_specs: [],
        isComplete: !!(title && core_principle && audio_suggestion && cta_implementation)
      });
    }

    // If no complete concepts found, look for partial ones with just titles
    if (concepts.length === 0) {
      const titleMatches = jsonText.match(/"title":\s*"([^"]*)"/g);
      if (titleMatches) {
        titleMatches.forEach((titleMatch) => {
          const titleResult = titleMatch.match(/"title":\s*"([^"]*)"/);
          if (titleResult) {
            const title = titleResult[1];

            // Try to find associated data for this title
            const titleIndex = jsonText.indexOf(titleMatch);
            const afterTitle = jsonText.substring(titleIndex);

            // Look for core_principle near this title
            const principleMatch = afterTitle.match(/"core_principle":\s*"([^"]*)"/);
            const audioMatch = afterTitle.match(/"audio_suggestion":\s*"([^"]*)"/);
            const ctaMatch = afterTitle.match(/"cta_implementation":\s*"([^"]*)"/);

            concepts.push({
              title,
              core_principle: principleMatch ? principleMatch[1] : 'Loading...',
              scene_prompts: [],
              audio_suggestion: audioMatch ? audioMatch[1] : 'Loading...',
              cta_implementation: ctaMatch ? ctaMatch[1] : 'Loading...',
              veo3_visual_specs: [],
              veo3_audio_specs: [],
              isComplete: !!(principleMatch && audioMatch && ctaMatch)
            });
          }
        });
      }
    }
  } catch (error) {
    console.error('Error extracting partial concepts:', error);
  }

  return concepts;
};

const VideoSciencePage = () => {
  const [uploadedVideo, setUploadedVideo] = useState<File | null>(null);
  const [productName, setProductName] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [callToAction, setCallToAction] = useState('');
  const [videoStyle, setVideoStyle] = useState('');
  const [includeViralAnatomy, setIncludeViralAnatomy] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [error, setError] = useState('');
  const [rawStreamContent, setRawStreamContent] = useState('');
  const [streamingConcepts, setStreamingConcepts] = useState<StreamingVideoConcept[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);

  // Cache keys for localStorage
  const CACHE_KEYS = useMemo(() => ({
    uploadedVideoData: 'videoscience_uploadedVideoData',
    productName: 'videoscience_productName',
    productDescription: 'videoscience_productDescription',
    targetAudience: 'videoscience_targetAudience',
    callToAction: 'videoscience_callToAction',
    videoStyle: 'videoscience_videoStyle',
    includeViralAnatomy: 'videoscience_includeViralAnatomy',
    generatedContent: 'videoscience_generatedContent'
  }), []);

  // Cache utility functions
  const getCacheInfo = useCallback(() => {
    let totalSize = 0;
    Object.values(CACHE_KEYS).forEach(key => {
      const item = localStorage.getItem(key);
      if (item) {
        totalSize += item.length;
      }
    });
    return {
      totalSize,
      formattedSize: (totalSize / 1024).toFixed(1) + ' KB'
    };
  }, [CACHE_KEYS]);

  const safeSetLocalStorage = useCallback((key: string, value: string) => {
    try {
      const currentSize = getCacheInfo().totalSize;
      const newItemSize = value.length;
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (currentSize + newItemSize > maxSize) {
        console.warn('Cache size limit approaching, consider clearing old data');
      }

      localStorage.setItem(key, value);
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
      if (error instanceof DOMException && error.code === 22) {
        alert('Browser storage is full. Please clear some data or use a smaller video file.');
      }
    }
  }, [getCacheInfo]);

  const clearCache = useCallback(() => {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }, [CACHE_KEYS]);

  const exportCacheData = useCallback(() => {
    const cacheData: any = {};
    Object.entries(CACHE_KEYS).forEach(([key, storageKey]) => {
      const value = localStorage.getItem(storageKey);
      if (value) {
        cacheData[key] = value;
      }
    });
    
    const dataStr = JSON.stringify(cacheData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `videoscience-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [CACHE_KEYS]);

  const importCacheData = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target?.result as string);
        
        Object.entries(importedData).forEach(([key, value]) => {
          if (CACHE_KEYS[key as keyof typeof CACHE_KEYS] && typeof value === 'string') {
            safeSetLocalStorage(CACHE_KEYS[key as keyof typeof CACHE_KEYS], value);
          }
        });
        
        window.location.reload();
      } catch (error) {
        console.error('Error importing cache data:', error);
        alert('Error importing backup file. Please check the file format.');
      }
    };
    reader.readAsText(file);
  }, [CACHE_KEYS, safeSetLocalStorage]);

  // Load cached data on component mount
  useEffect(() => {
    const loadCachedData = () => {
      try {
        const cachedProductName = localStorage.getItem(CACHE_KEYS.productName);
        const cachedProductDescription = localStorage.getItem(CACHE_KEYS.productDescription);
        const cachedTargetAudience = localStorage.getItem(CACHE_KEYS.targetAudience);
        const cachedCallToAction = localStorage.getItem(CACHE_KEYS.callToAction);
        const cachedVideoStyle = localStorage.getItem(CACHE_KEYS.videoStyle);
        const cachedIncludeViralAnatomy = localStorage.getItem(CACHE_KEYS.includeViralAnatomy);
        const cachedGeneratedContent = localStorage.getItem(CACHE_KEYS.generatedContent);
        const cachedUploadedVideoData = localStorage.getItem(CACHE_KEYS.uploadedVideoData);

        if (cachedProductName) setProductName(cachedProductName);
        if (cachedProductDescription) setProductDescription(cachedProductDescription);
        if (cachedTargetAudience) setTargetAudience(cachedTargetAudience);
        if (cachedCallToAction) setCallToAction(cachedCallToAction);
        if (cachedVideoStyle) setVideoStyle(cachedVideoStyle);
        if (cachedIncludeViralAnatomy) setIncludeViralAnatomy(cachedIncludeViralAnatomy === 'true');
        
        if (cachedGeneratedContent) {
          const parsedContent = JSON.parse(cachedGeneratedContent);
          setGeneratedContent(parsedContent);
        }

        if (cachedUploadedVideoData) {
          try {
            const videoData = JSON.parse(cachedUploadedVideoData);
            const byteCharacters = atob(videoData.data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const file = new File([byteArray], videoData.name, { type: videoData.type });
            setUploadedVideo(file);
          } catch (videoError) {
            console.error('Error restoring video from cache:', videoError);
            localStorage.removeItem(CACHE_KEYS.uploadedVideoData);
          }
        }
      } catch (error) {
        console.error('Error loading cached data:', error);
        clearCache();
      }
    };

    loadCachedData();
  }, [CACHE_KEYS, clearCache]);

  // Cache form data whenever it changes
  useEffect(() => {
    safeSetLocalStorage(CACHE_KEYS.productName, productName);
  }, [productName, CACHE_KEYS.productName, safeSetLocalStorage]);

  useEffect(() => {
    safeSetLocalStorage(CACHE_KEYS.productDescription, productDescription);
  }, [productDescription, CACHE_KEYS.productDescription, safeSetLocalStorage]);

  useEffect(() => {
    safeSetLocalStorage(CACHE_KEYS.targetAudience, targetAudience);
  }, [targetAudience, CACHE_KEYS.targetAudience, safeSetLocalStorage]);

  useEffect(() => {
    safeSetLocalStorage(CACHE_KEYS.callToAction, callToAction);
  }, [callToAction, CACHE_KEYS.callToAction, safeSetLocalStorage]);

  useEffect(() => {
    safeSetLocalStorage(CACHE_KEYS.videoStyle, videoStyle);
  }, [videoStyle, CACHE_KEYS.videoStyle, safeSetLocalStorage]);

  useEffect(() => {
    safeSetLocalStorage(CACHE_KEYS.includeViralAnatomy, includeViralAnatomy.toString());
  }, [includeViralAnatomy, CACHE_KEYS.includeViralAnatomy, safeSetLocalStorage]);

  useEffect(() => {
    if (generatedContent) {
      safeSetLocalStorage(CACHE_KEYS.generatedContent, JSON.stringify(generatedContent));
    }
  }, [generatedContent, CACHE_KEYS.generatedContent, safeSetLocalStorage]);

  useEffect(() => {
    if (uploadedVideo) {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;
        const videoData = {
          name: uploadedVideo.name,
          type: uploadedVideo.type,
          size: uploadedVideo.size,
          data: base64.split(',')[1]
        };
        safeSetLocalStorage(CACHE_KEYS.uploadedVideoData, JSON.stringify(videoData));
      };
      reader.readAsDataURL(uploadedVideo);
    }
  }, [uploadedVideo, CACHE_KEYS.uploadedVideoData, safeSetLocalStorage]);

  const handleVideoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('video/')) {
        setError('Please upload a valid video file.');
        setTimeout(() => setError(''), 5000);
        return;
      }

      // Validate file size (4MB limit for serverless processing)
      const maxSize = 4 * 1024 * 1024;
      if (file.size > maxSize) {
        setError('Video file size must be less than 4MB for serverless processing.');
        setTimeout(() => setError(''), 5000);
        return;
      }

      setUploadedVideo(file);
      setError('');
    }
  };

  const removeVideo = () => {
    setUploadedVideo(null);
    localStorage.removeItem(CACHE_KEYS.uploadedVideoData);
  };

  const handleAnalyze = async () => {
    if (!uploadedVideo) {
      setError('Please upload a video file.');
      setTimeout(() => setError(''), 5000);
      return;
    }

    setAnalysisLoading(true);
    setError('');
    setGeneratedContent(null);

    try {
      const formData = new FormData();
      formData.append('video', uploadedVideo);

      const response = await fetch('/api/analyze-video-science', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = 'Failed to analyze video.';
        try {
          const errorText = await response.text();
          console.error('Error response:', errorText);

          // Try to parse as JSON first
          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.message || errorMessage;
          } catch {
            // If not JSON, use the text as is (but limit length)
            errorMessage = errorText.length > 100 ? errorText.substring(0, 100) + '...' : errorText;
          }
        } catch (readError) {
          console.error('Failed to read error response:', readError);
        }
        throw new Error(errorMessage);
      }

      const analysisResult = await response.json();

      if (!analysisResult.videoAnalysis) {
        console.error('Invalid response structure:', analysisResult);
        throw new Error('Invalid response format from server.');
      }

      // Ensure the viralAnatomy array exists
      if (!analysisResult.videoAnalysis.viralAnatomy) {
        console.warn('Missing viralAnatomy array in response, initializing empty array');
        analysisResult.videoAnalysis.viralAnatomy = [];
      }

      setGeneratedContent(prev => ({
        ...prev,
        videoAnalysis: {
          fileName: uploadedVideo?.name,
          viralAnatomy: analysisResult.videoAnalysis.viralAnatomy || []
        }
      } as GeneratedContent));

    } catch (error) {
      console.error('Analysis error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze video.';

      if (errorMessage.includes('Unable to process video from')) {
        setError(`${errorMessage}\n\nTip: Try downloading the video from the social media platform and uploading it directly instead.`);
      } else {
        setError(errorMessage);
      }

      setTimeout(() => setError(''), 10000);
    } finally {
      setAnalysisLoading(false);
    }
  };



  const handleGenerate = async () => {
    if (!productName.trim() || !productDescription.trim() || !targetAudience.trim() || !callToAction.trim()) {
      setError('Please fill in all required product information fields.');
      setTimeout(() => setError(''), 5000);
      return;
    }

    if (!generatedContent?.videoAnalysis) {
      setError('Please analyze a video first.');
      setTimeout(() => setError(''), 5000);
      return;
    }

    setLoading(true);
    setError('');
    setRawStreamContent('');
    setStreamingConcepts([]);
    setIsStreaming(true);

    try {
      const response = await fetch('/api/generate-video-concepts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoAnalysis: generatedContent.videoAnalysis,
          productName,
          productDescription,
          targetAudience,
          callToAction,
          videoStyle: videoStyle.trim() || undefined,
          includeViralAnatomy,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate video concepts.');
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let receivedText = '';

      if (reader) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            receivedText += chunk;
            setRawStreamContent(receivedText);

            // Try to parse complete JSON at the end
            try {
              const firstBrace = receivedText.indexOf('{');
              const lastBrace = receivedText.lastIndexOf('}');

              if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
                const jsonString = receivedText.substring(firstBrace, lastBrace + 1);
                const parsedContent = JSON.parse(jsonString);

                // Handle both old and new JSON structure
                let videoConcepts = null;
                if (parsedContent && parsedContent.video_concepts && Array.isArray(parsedContent.video_concepts)) {
                  videoConcepts = parsedContent.video_concepts;
                } else if (parsedContent && parsedContent.videoConcepts && Array.isArray(parsedContent.videoConcepts)) {
                  videoConcepts = parsedContent.videoConcepts;
                }

                if (videoConcepts && videoConcepts.length > 0) {
                  // Only update the final state when streaming is complete
                  setGeneratedContent(prev => ({
                    ...prev!,
                    videoConcepts: videoConcepts
                  }));

                  // Keep the raw content visible for a moment before completing
                  if (done) {
                    setTimeout(() => {
                      setRawStreamContent('');
                      setIsStreaming(false);
                    }, 1000);
                    break;
                  }
                }
              }
            } catch (parseError) {
              // JSON not complete yet, continue streaming
            }
          }
        } catch (streamError) {
          console.error('Streaming error:', streamError);
          throw streamError;
        }
      }

    } catch (error) {
      console.error('Generation error:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate video concepts.');
      setTimeout(() => setError(''), 5000);
    } finally {
      setLoading(false);
      setIsStreaming(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-2">Video Science</h1>
          <p className="text-gray-600">
            Upload a viral video to analyze behavioral science principles that make it engaging.
            Then generate 10 unique video concepts for your product that leverage those same psychological triggers.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Cache Status Indicator */}
          {(productName || uploadedVideo || generatedContent || videoStyle) && (
            <div className="flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Data Saved ({getCacheInfo().formattedSize})
            </div>
          )}

          {/* Export/Import Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={exportCacheData}
              disabled={!productName && !uploadedVideo && !generatedContent && !videoStyle}
              className="px-3 py-1 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Export your data as a backup file"
            >
              Export Data
            </button>

            <label className="px-3 py-1 text-xs text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors duration-200 cursor-pointer">
              Import Data
              <input
                type="file"
                accept=".json"
                onChange={importCacheData}
                className="hidden"
              />
            </label>

            {/* Clear All Button */}
            <button
              onClick={() => {
                if (confirm('Are you sure you want to clear all saved data and tell the LLM to forget all prompts? This action cannot be undone.')) {
                  clearCache();
                  setUploadedVideo(null);
                  setProductName('');
                  setProductDescription('');
                  setTargetAudience('');
                  setCallToAction('');
                  setVideoStyle('');
                  setIncludeViralAnatomy(false);
                  setGeneratedContent(null);
                  setRawStreamContent('');
                  setError('');
                  // Send a request to tell the LLM to forget previous prompts
                  fetch('/api/generate-video-concepts', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      forgetPreviousPrompts: true
                    }),
                  }).catch(err => console.error('Error sending forget prompt:', err));
                }
              }}
              className="px-3 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors duration-200"
            >
              Clear All
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Cache Info Notice */}
      <div className="mb-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <svg className="w-4 h-4 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="text-sm text-blue-700">
              <strong>Auto-Save Enabled:</strong> Your form data, uploaded videos, and analysis results are automatically saved to your browser.
              You can safely refresh the page without losing your progress.
            </p>
          </div>
        </div>
      </div>

      {/* Step 1: Viral Video Analysis */}
      <div className="mb-8">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center mb-4">
              <div className="bg-indigo-100 p-2 rounded-full mr-3">
                <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Step 1: Viral Video Analysis</h2>
                <p className="text-gray-600 text-sm mt-1">
                  Upload a viral video to analyze behavioral science principles that make it engaging.
                </p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Video File (MP4, MOV, AVI - Max 4MB)
              </label>
              <input
                type="file"
                accept="video/*"
                onChange={handleVideoUpload}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
              {uploadedVideo && (
                <p className="text-sm text-green-600 mt-1">
                  ✓ {uploadedVideo.name} ({(uploadedVideo.size / 1024 / 1024).toFixed(1)}MB)
                </p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                For files larger than 4MB, please compress your video using online tools or video editing software before uploading.
              </p>
            </div>

            <div className="mt-4">
              <button
                onClick={handleAnalyze}
                disabled={analysisLoading || !uploadedVideo}
                className="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center"
              >
                {analysisLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Analyzing Video...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Analyze Behavioral Science
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Video Analysis Results */}
          {generatedContent?.videoAnalysis && generatedContent.videoAnalysis.viralAnatomy && generatedContent.videoAnalysis.viralAnatomy.length > 0 && (
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Viral Anatomy Analysis</h3>
              <p className="text-sm text-gray-600 mb-4">
                Analysis of the 7 key behavioral science elements that make content go viral:
              </p>
              <div className="grid gap-4">
                {generatedContent.videoAnalysis.viralAnatomy.map((element, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">{element.name}</h4>
                    <p className="text-gray-700 text-sm mb-2">{element.explanation}</p>
                    <p className="text-indigo-600 text-sm">
                      <strong>Video Example:</strong> {element.videoExample}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Component 2: Product & Campaign Input Form */}
      {generatedContent?.videoAnalysis && (
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Step 2: Tell Us About Your Product</h2>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Name *
                </label>
                <input
                  type="text"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g., EcoClean Detergent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Audience *
                </label>
                <input
                  type="text"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g., Busy moms, Gen Z gamers"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Description *
                </label>
                <textarea
                  value={productDescription}
                  onChange={(e) => setProductDescription(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Describe your product, its benefits, and what makes it unique..."
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Call to Action (CTA) *
                </label>
                <input
                  type="text"
                  value={callToAction}
                  onChange={(e) => setCallToAction(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g., Shop Now, Learn More, Get yours today"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video Style & Direction (Optional)
                </label>
                <textarea
                  value={videoStyle}
                  onChange={(e) => setVideoStyle(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Describe the type of video concepts you want: e.g., 'Controversial with strong hooks similar to the uploaded video', 'POV first-person perspective', 'UGC with organic authentic look', 'Educational explainer style', 'Emotional storytelling approach', etc."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Tell the AI what style, tone, or approach you want for your video concepts. This helps create more targeted and relevant ideas.
                </p>

                <div className="bg-blue-50 rounded-lg p-4 mt-3">
                  <h4 className="font-semibold text-blue-900 mb-2">Example Directions:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• &quot;Controversial with strong hooks similar to the uploaded video&quot;</li>
                    <li>• &quot;POV first-person perspective using &apos;I&apos;, &apos;we&apos;, &apos;me&apos; language&quot;</li>
                    <li>• &quot;UGC with organic authentic look and feel&quot;</li>
                    <li>• &quot;Educational explainer style with clear value propositions&quot;</li>
                    <li>• &quot;Emotional storytelling approach focusing on transformation&quot;</li>
                    <li>• &quot;Aggressive sales-focused with urgency and scarcity&quot;</li>
                    <li>• &quot;Curiosity-driven hooks that create knowledge gaps&quot;</li>
                    <li>• &quot;Problem-solution format highlighting pain points&quot;</li>
                    <li>• &quot;Social proof and testimonial-style content&quot;</li>
                    <li>• &quot;Trend-jacking current viral formats or memes&quot;</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Viral Anatomy Checkbox */}
            {generatedContent?.videoAnalysis && generatedContent.videoAnalysis.viralAnatomy && generatedContent.videoAnalysis.viralAnatomy.length > 0 && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="includeViralAnatomy"
                    checked={includeViralAnatomy}
                    onChange={(e) => setIncludeViralAnatomy(e.target.checked)}
                    className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <div className="ml-3">
                    <label htmlFor="includeViralAnatomy" className="text-sm font-medium text-yellow-800">
                      Include Viral Anatomy Elements in Video Concepts
                    </label>
                    <p className="text-xs text-yellow-700 mt-1">
                      When checked, the AI will incorporate the specific viral anatomy elements identified from your uploaded video into the generated video concepts, helping replicate the viral success pattern.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <button
              onClick={handleGenerate}
              disabled={loading || !productName.trim() || !productDescription.trim() || !targetAudience.trim() || !callToAction.trim()}
              className="w-full mt-6 bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Generating 10 Viral Video Ideas...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Generate 10 Viral Video Ideas
                </>
              )}
            </button>
          </div>
        </div>
      )}

      {/* Component 3: Streaming Video Concepts */}
      {isStreaming && rawStreamContent && (
        <div className="mb-8">
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mr-3"></div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">Generating Your Viral Video Playbook...</h2>
                    <p className="text-sm text-gray-600 mt-1">
                      AI is analyzing behavioral science principles and creating personalized video concepts...
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-indigo-600">{rawStreamContent.length}</div>
                  <div className="text-xs text-gray-500">Characters</div>
                </div>
              </div>
              <div className="mt-4 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-indigo-600 h-2 rounded-full transition-all duration-300 animate-pulse"
                  style={{ width: `${Math.min((rawStreamContent.length / 10000) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
            <div className="p-6 bg-gray-50">
              <div className="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-gray-400 text-sm ml-2">Live Stream Output</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400 text-xs">Streaming</span>
                  </div>
                </div>
                <pre className="text-green-400 text-sm font-mono leading-relaxed whitespace-pre-wrap break-words">
                  {rawStreamContent}
                  <span className="animate-pulse">▊</span>
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}



      {generatedContent?.videoConcepts && (
        <div className="mb-8">
          <div className="flex items-center mb-6">
            <div className="bg-green-100 p-2 rounded-full mr-3">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">Your Viral Video Playbook</h2>
              <p className="text-gray-600 mb-2">
                {generatedContent.videoConcepts.length} video concepts based on behavioral science principles
              </p>
              <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Optimized for Veo3 AI Video Generation
              </div>
            </div>
          </div>

          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h4 className="text-sm font-semibold text-blue-900 mb-1">Ultra-Detailed Veo3 Cinema-Grade Prompts + Complete Scene Implementation</h4>
                <p className="text-xs text-blue-700 mb-2">
                  Each concept features both hyper-detailed scene specifications for Super Bowl commercial-quality results AND complete 8-second Veo3-ready scenes combining visual storytelling with integrated audio design.
                </p>
                <ul className="text-xs text-blue-600 space-y-1">
                  <li>• <strong>Technical Precision:</strong> Exact camera specs (lens, aperture, distance), lighting setup, and timing</li>
                  <li>• <strong>Audio Engineering:</strong> Precise sound design with dB levels, frequency characteristics, and spatial positioning</li>
                  <li>• <strong>Psychological Triggers:</strong> Neurological impact analysis and emotional response optimization</li>
                  <li>• <strong>Cinema Standards:</strong> Professional color grading, depth of field, and material physics specifications</li>
                  <li>• <strong>Human Authenticity:</strong> Micro-expressions, natural movements, and realistic timing for maximum believability</li>
                  <li>• <strong className="text-amber-600">NEW - Enhanced Veo3 Complete Scene:</strong> <span className="text-amber-600">Combined visual storytelling and layered audio design in one unified specification</span></li>
                </ul>
              </div>
            </div>
          </div>

          <div className="grid gap-6">
            {generatedContent.videoConcepts.map((concept, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start">
                      <div className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg w-10 h-10 flex items-center justify-center text-sm font-bold mr-4">
                        {index + 1}
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-1">
                          {concept.title}
                        </h3>
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          {concept.core_principle}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                          <svg className="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          Scene-by-Scene Prompts
                        </h4>
                        <div className="space-y-4">
                          {concept.scene_prompts.map((prompt, promptIndex) => {
                            const sceneMatch = prompt.match(/Scene\s+\d+\s*\(([^)]+)\):\s*(.+?)(?:Audio:\s*(.+?))?(?:\s*This moment.+)?$/i);
                            const audioMatch = prompt.match(/Audio:\s*(.+?)(?:\s*This moment|$)/i);
                            const emotionalMatch = prompt.match(/This moment (.+?)$/i);

                            const duration = sceneMatch ? sceneMatch[1] : null;
                            const visualPart = sceneMatch ? sceneMatch[2].trim() : prompt.replace(/Audio:\s*.+/i, '').trim();
                            const audioPart = audioMatch ? audioMatch[1].trim() : null;
                            const emotionalTrigger = emotionalMatch ? emotionalMatch[1].trim() : null;

                            return (
                              <div key={promptIndex} className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                <div className="flex items-start mb-3">
                                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg w-8 h-8 flex items-center justify-center text-sm font-bold mr-3 mt-0.5 flex-shrink-0">
                                    {promptIndex + 1}
                                  </div>
                                  <div className="flex-1">
                                    {duration && (
                                      <div className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium mb-2">
                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        {duration}
                                      </div>
                                    )}

                                    <div className="bg-gray-50 rounded-md p-3 mb-3">
                                      <h5 className="text-xs font-semibold text-gray-600 mb-1 uppercase tracking-wide">Visual Specification</h5>
                                      <p className="text-sm text-gray-800 leading-relaxed font-mono">{visualPart}</p>
                                    </div>

                                    {audioPart && (
                                      <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-3">
                                        <div className="flex items-center mb-2">
                                          <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                                          </svg>
                                          <span className="text-xs font-semibold text-green-700 uppercase tracking-wide">Audio Design</span>
                                        </div>
                                        <p className="text-sm text-green-800 leading-relaxed font-mono">{audioPart}</p>
                                      </div>
                                    )}

                                    {emotionalTrigger && (
                                      <div className="bg-purple-50 border border-purple-200 rounded-md p-3">
                                        <div className="flex items-center mb-1">
                                          <svg className="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                          </svg>
                                          <span className="text-xs font-semibold text-purple-700 uppercase tracking-wide">Psychological Impact</span>
                                        </div>
                                        <p className="text-sm text-purple-800 leading-relaxed italic">{emotionalTrigger}</p>
                                      </div>
                                    )}

                                    {concept.veo3_visual_specs && concept.veo3_visual_specs[promptIndex] && concept.veo3_audio_specs && concept.veo3_audio_specs[promptIndex] && (
                                      <div className="bg-gradient-to-r from-amber-50 to-cyan-50 border border-amber-200 rounded-md p-3 mt-3">
                                        <div className="flex items-center justify-between mb-3">
                                          <div className="flex items-center">
                                            <svg className="w-4 h-4 mr-2 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                                            </svg>
                                            <span className="text-xs font-semibold text-amber-700 uppercase tracking-wide">Veo3 Complete Scene</span>
                                          </div>
                                          <div className="flex items-center space-x-1">
                                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                              8s Format
                                            </span>
                                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">
                                              Enhanced Detail
                                            </span>
                                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                              Layered Audio
                                            </span>
                                          </div>
                                        </div>
                                        <div className="space-y-3">
                                          <p className="text-sm text-gray-800 leading-relaxed">
                                            {concept.veo3_visual_specs[promptIndex]}
                                            <span className="text-cyan-700 font-medium"> (Audio: {concept.veo3_audio_specs[promptIndex]})</span>
                                          </p>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="bg-green-50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                          <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                          </svg>
                          Suggested Audio
                        </h4>
                        <p className="text-sm text-gray-700 bg-white rounded-md p-3 border border-green-200">{concept.audio_suggestion}</p>
                      </div>

                      <div className="bg-purple-50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                          <svg className="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                          </svg>
                          CTA Implementation
                        </h4>
                        <p className="text-sm text-gray-700 bg-white rounded-md p-3 border border-purple-200">{concept.cta_implementation}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoSciencePage;
