"use client";

import React, { useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const CompetingAgainstLuckPage = () => {
  const [bookContent, setBookContent] = useState('');

  useEffect(() => {
    const fetchBookContent = async () => {
      try {
        const response = await fetch('/book_summary.md');
        if (!response.ok) {
          throw new Error('HTTP error! status: ' + response.status);
        }
        const text = await response.text();
        setBookContent(text);
      } catch (error) {
console.error('Failed to load book content:', error);
setBookContent("Error loading book content.");
      }
    };
    fetchBookContent();
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Competing Against Luck by <PERSON></h1>
      <p className="mb-6">This page contains the summary and actionable teachings from the book &#34;Competing Against Luck&#34;.</p>
      <div className="prose prose-indigo max-w-none">
        {bookContent ? (
          <ReactMarkdown remarkPlugins={[remarkGfm]}>{bookContent}</ReactMarkdown>
        ) : (
          <p>Loading book content...</p>
        )}
      </div>
    </div>
  );
};

export default CompetingAgainstLuckPage;
