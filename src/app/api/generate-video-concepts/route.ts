import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const { videoAnalysis, productName, productDescription, targetAudience, callToAction, videoStyle, includeViralAnatomy, forgetPreviousPrompts } = await request.json();

    // Handle forget previous prompts request
    if (forgetPreviousPrompts) {
      return NextResponse.json({ message: 'Previous prompts forgotten successfully.' }, { status: 200 });
    }

    if (!videoAnalysis || !productName || !productDescription || !targetAudience || !callToAction) {
      return NextResponse.json({ message: 'Missing required fields.' }, { status: 400 });
    }

    // Build behavioral science context from video analysis
    let behavioralScienceContext = '';
    if (includeViralAnatomy && videoAnalysis && videoAnalysis.viralAnatomy && videoAnalysis.viralAnatomy.length > 0) {
      behavioralScienceContext = `VIRAL ANATOMY INSIGHTS FROM UPLOADED VIDEO ANALYSIS:
${videoAnalysis.viralAnatomy.map((element: any, index: number) => `
${index + 1}. ${element.name}
   - Explanation: ${element.explanation}
   - Video Example: ${element.videoExample}
`).join('')}

IMPORTANT: Incorporate these viral anatomy elements into the video concepts to replicate the viral success pattern of the analyzed video.`;
    } else if (videoAnalysis && videoAnalysis.principles) {
      const behavioralPrinciplesJson = JSON.stringify(videoAnalysis.principles, null, 2);
      behavioralScienceContext = `Core Behavioral Science Principles to Use (from analysis):
${behavioralPrinciplesJson}`;
    } else {
      behavioralScienceContext = `BEHAVIORAL SCIENCE PRINCIPLES TO APPLY:
1. Hook Principle - Start with attention-grabbing opening (curiosity, surprise, controversy)
2. Emotional Connection - Trigger emotions like joy, surprise, fear, or aspiration
3. Relatability & Identity Expression - Make viewers feel seen and understood
4. Concise and Engaging Storytelling - Tell stories quickly and effectively
5. Shareability - Create content people want to share with others
6. Timeliness - Connect to current trends, events, or cultural moments
7. Authenticity - Make content feel genuine and trustworthy`;
    }

    const prompt = `You are an elite creative director for a top-tier social media marketing agency. Your task is to generate 10 distinct, highly-detailed video concepts for a client's product based on a psychological analysis of a successful viral video.

Client Product Information:

Product Name: ${productName}

Product Description: ${productDescription}

Target Audience: ${targetAudience}

Call to Action: ${callToAction}

${videoStyle ? `Video Style & Direction: ${videoStyle}

IMPORTANT: The client has specifically requested the following style/approach for their video concepts. Make sure ALL 20 concepts align with this direction while still incorporating the behavioral science principles below.

` : ''}${behavioralScienceContext}

Your Task:
Create 10 unique video concepts${videoStyle ? ` that align with the client's specified style/direction` : ''}. For each concept, you must creatively apply one or more of the principles above to the client's product${videoStyle ? ` while ensuring the concepts match the requested video style and approach` : ''}. Each scene prompt must be EXTREMELY DETAILED and SPECIFIC to ensure Veo3 generates highly convincing, professional-quality videos. Include precise camera work, lighting, timing, movements, expressions, and comprehensive audio design.

Return your response ONLY as a JSON object. The root should be a single key "video_concepts" which contains an array of 10 concept objects. Each concept object must have the following keys: title, core_principle, scene_prompts (which is an array of strings), audio_suggestion, cta_implementation, veo3_visual_specs (which is an array of strings), and veo3_audio_specs (which is an array of strings).

Example structure for a single concept object (EXTREMELY DETAILED):

{
  "title": "The Ultimate Unboxing ASMR Experience",
  "core_principle": "ASMR (Sensory Appeal)",
  "scene_prompts": [
    "Scene 1 (4 seconds): Ultra-macro shot, 100mm lens, shallow depth of field (f/1.4). A perfectly manicured hand with subtle nail polish slowly approaches pristine ${productName} packaging. Camera starts 6 inches away, slowly dollies in to 2 inches. Soft, diffused studio lighting from 45-degree angle creates gentle shadows. Hand moves at 0.5x speed, fingers delicately grip corner of holographic sticker. Audio: Complete silence for first 1.5 seconds, then crisp, satisfying peeling SFX begins - high-frequency crackling sound at 85dB, heavily amplified with slight reverb, lasting exactly 2.5 seconds with gradual fade.",
    "Scene 2 (3 seconds): Perfect top-down view, camera locked at 24 inches above surface. Clean white marble surface with subtle veining, lit by soft box lighting creating even illumination. ${productName} descends into frame from top-right at controlled speed, rotating 15 degrees clockwise during descent. Product settles with slight bounce physics. Audio: 0.8 seconds of ambient room tone, followed by deep, resonant 'thud' SFX at 78dB with 0.3-second natural decay, slight bass emphasis at 60Hz.",
    "Scene 3 (3 seconds): Extreme close-up, 85mm macro lens, camera positioned at 30-degree angle to product surface. Shallow focus on finger approaching button, background completely blurred. Finger has slight tremor of anticipation, pauses 0.5 seconds before contact. Button depression shows 2mm travel with realistic material compression. Audio: Pin-drop silence for 1.2 seconds building tension, then sharp, tactile 'click' SFX - crisp mechanical sound at 82dB with metallic overtones, followed by complete silence for remaining 1.8 seconds."
  ],
  "audio_suggestion": "Overall Mood: Ultra-minimalist sensory experience. Zero background music or ambient sound. Focus entirely on isolated, high-fidelity product interaction sounds recorded in anechoic chamber quality. Each sound effect should feel intimate and immediate, as if viewer is wearing high-end headphones. Use dynamic range compression to enhance quiet details while maintaining impact of louder sounds.",
  "cta_implementation": "Final scene (2 seconds): Product in perfect hero shot, centered frame, soft gradient background. Elegant typography fades in: '${callToAction}' in premium sans-serif font, followed by smaller text 'Experience the satisfaction.' Camera slowly pushes in 10% while maintaining focus. Audio: Gentle, barely audible breath-like whoosh as text appears.",
  "veo3_visual_specs": [
    "A person's hand moves gracefully toward the pristine ${productName} packaging, which sits elegantly on a clean surface. Their fingers, with perfectly manicured nails, delicately grip the corner of a shimmering holographic sticker. The sticker catches light as they begin peeling it off in one smooth, satisfying motion. The packaging material responds naturally, creating subtle wrinkles and shadows as the seal breaks. The person's expression shows focused concentration and anticipation.",
    "The ${productName} appears from above the frame, slowly descending toward a spotless white marble surface with subtle natural veining. As it falls, the product rotates gently clockwise, showing its sleek design from multiple angles. It lands with a soft, controlled bounce that demonstrates quality and substance. The marble surface reflects the product subtly, creating depth and luxury appeal.",
    "A finger hovers just above the ${productName}'s main button, trembling slightly with anticipation. The person takes a brief pause, building suspense, before making contact. The button depresses smoothly, showing premium tactile feedback and quality construction. A subtle indicator light activates, confirming the action. The person's face shows satisfaction and delight at the responsive interaction."
  ],
  "veo3_audio_specs": [
    "Soft ambient room tone creates intimate atmosphere. Complete silence builds anticipation for 2 seconds, then a crisp, satisfying peeling sound emerges with delicate crackling texture, like unwrapping a premium gift. Person whispers 'Perfect' under their breath as the seal breaks.",
    "Gentle ambient room tone continues. A soft whoosh accompanies the product's descent. Deep, resonant thud as product makes contact with marble, followed by two smaller settling sounds. Person says 'Beautiful' in an appreciative tone.",
    "Pin-drop silence creates tension and focus. Sharp, premium mechanical click sound with satisfying tactile quality. Soft electronic chime confirms activation. Person exhales with satisfaction and says 'That's exactly what I wanted' with genuine delight."
  ]
}

CRITICAL REQUIREMENTS FOR VEO3 HYPER-REALISTIC GENERATION:

1. **Ultra-Detailed Scene Specifications:**
   - Each scene MUST be 2-6 seconds with exact timing specified
   - Include precise camera specs: lens focal length (24mm, 50mm, 85mm, 100mm macro), aperture (f/1.4, f/2.8, f/5.6), distance from subject
   - Specify exact camera movements with speed and direction: "slow dolly in from 3 feet to 18 inches over 4 seconds", "gentle 15-degree pan left", "subtle handheld shake", "locked tripod shot"
   - Detail lighting setup: "soft box from 45-degree angle", "natural window light with 2-stop fill", "dramatic side lighting with hard shadows", "even studio lighting at 5600K"
   - Describe exact subject movements, speeds, and physics: "hand moves at 0.7x speed", "product rotates 30 degrees clockwise", "liquid pours at realistic viscosity"
   - Include environmental details: surface textures, background elements, atmospheric conditions

2. **Hyper-Detailed Audio Design:**
   - Specify EXACT sound effects with technical precision: "metallic clink with 2kHz resonance", "soft whoosh with low-frequency rumble at 80Hz", "crisp snap with sharp attack and 0.2s decay"
   - Include precise volume levels in dB: "whisper-quiet at 45dB", "normal conversation at 65dB", "amplified to 85dB", "barely audible at 35dB"
   - Specify exact timing with millisecond precision: "sound begins at 1.2 seconds", "0.5s delay with 0.3s fade-in", "abrupt stop at 2.7 seconds", "crescendo over exactly 1.8 seconds"
   - Detail frequency characteristics: "bass emphasis at 60Hz", "high-frequency sparkle at 8kHz", "mid-range clarity at 1kHz"
   - Include spatial audio details: "sound panned 30% left", "centered with slight reverb", "close-mic intimacy", "distant echo with 0.8s decay"
   - Specify silence strategically: "complete silence for tension", "room tone at -40dB", "dead silence for 0.5s before impact"
   - Include emotional audio descriptors: "satisfying with dopamine-triggering frequency response", "ASMR-inducing with binaural qualities"

3. **Psychological Trigger Optimization:**
   - Each concept must exploit specific neurological pathways identified in the behavioral analysis
   - Detail how visual elements trigger dopamine, serotonin, or adrenaline responses
   - Specify micro-expressions, body language, and facial reactions that enhance psychological impact
   - Include subliminal visual cues: color psychology, golden ratio compositions, symmetry/asymmetry for attention
   - Design audio to trigger ASMR, nostalgia, urgency, or satisfaction responses

4. **Cinema-Grade Production Standards:**
   - All concepts must achieve commercial/film production quality
   - Specify color grading: "warm 3200K temperature with +0.3 magenta tint", "high contrast with crushed blacks", "desaturated with teal/orange color scheme"
   - Include precise depth of field: "subject sharp, background at f/1.4 bokeh", "deep focus at f/8 for environmental context"
   - Detail texture and material properties: "matte surface with subtle fingerprint smudges", "glossy reflection with 80% specularity", "fabric with visible weave pattern"
   - Specify human elements: "genuine micro-expressions of surprise", "natural hand tremor showing anticipation", "authentic eye movement patterns"

5. **Veo3 Advanced Technical Optimization:**
   - Design for 9:16 vertical (TikTok/Instagram), 16:9 horizontal (YouTube), or 1:1 square formats
   - Each scene must have single primary focus point for AI clarity
   - Avoid complex multi-element scenes that confuse AI generation
   - Include realistic physics: gravity, momentum, material behavior, liquid dynamics
   - Specify practical effects over CGI: real steam, actual reflections, genuine textures
   - Design transitions: "match cut on circular motion", "fade through white", "seamless zoom transition"
   - Include human authenticity markers: natural breathing, micro-movements, realistic timing

MANDATORY SCENE STRUCTURE FOR MAXIMUM IMPACT:

Each scene prompt MUST include ALL of these elements:
1. **Exact Duration**: "Scene X (3.2 seconds):"
2. **Camera Specification**: Lens, aperture, distance, movement
3. **Lighting Setup**: Type, angle, temperature, intensity
4. **Subject Details**: Position, movement speed, material properties
5. **Environmental Context**: Surface, background, atmospheric conditions
6. **Audio Timeline**: Exact timing, volume, frequency characteristics
7. **Emotional Trigger**: How this specific moment creates psychological impact

EXAMPLE SCENE BREAKDOWN:
"Scene 1 (4.1 seconds): 85mm macro lens at f/2.0, camera 8 inches from subject, slow dolly in to 5 inches over 3 seconds. Soft key light from 60-degree angle at 3200K, subtle fill light at 20% intensity. Subject's hand approaches ${productName} with slight tremor showing anticipation, fingertips make contact at 2.3-second mark. Pristine white marble surface with subtle gray veining, background falls to pure black at f/2.0. Audio: Complete silence for first 2.3 seconds building psychological tension, then satisfying tactile contact sound at 78dB with 1.5kHz resonance and 0.4-second natural decay. This moment triggers anticipation-reward cycle, activating dopamine pathways through delayed gratification."

6. **Simplified Veo3 Direct Implementation Specifications:**
   - In addition to the detailed scene prompts, provide simplified versions optimized for direct Veo3 implementation
   - Create two arrays: veo3_visual_specs and veo3_audio_specs (one entry per scene)
   - veo3_visual_specs should be detailed scene descriptions without technical camera/lighting jargon
   - Write longer, more descriptive visual narratives that paint a complete picture of the scene
   - Focus on visual storytelling, character actions, object interactions, and environmental details
   - Describe movements, expressions, textures, colors, and spatial relationships naturally
   - If humans are involved, describe their actions, expressions, and body language in detail
   - Remove all technical specifications (lens types, f-stops, exact timing, etc.) but keep rich visual detail
   - Example detailed visual: "A person's hand carefully cradles a large, perfectly round potato with smooth, golden-brown skin. The potato sits steady in their palm as a gleaming, oversized chef's knife with a futuristic design slowly descends from above. The blade catches and reflects a sharp spotlight, creating a brilliant glint that dances across its polished surface. The person's fingers remain perfectly still, creating tension as the knife approaches the potato's surface. The background fades to deep shadows, focusing all attention on this moment of anticipation."
   - veo3_audio_specs should include layered audio design with ambient sounds, specific sound effects, and human speech when applicable
   - Example enhanced audio: "Soft ambient kitchen sounds create a subtle backdrop. A gentle whoosh builds as the knife moves through the air. The blade creates a sharp, crystalline 'ting' sound as light hits its surface. If person speaks: 'This is the moment of truth' in a confident, steady voice. Background fades to emphasize the knife's approach."

ENHANCED VEO3 STRUCTURE:
Each concept must include:
1. veo3_visual_specs array: One entry per scene with detailed visual storytelling (2-3 sentences each)
   - Paint a complete visual picture without technical jargon
   - Include character actions, expressions, object details, spatial relationships
   - Describe textures, colors, movements, and environmental context
   - Focus on visual narrative that Veo3 can easily interpret and generate

2. veo3_audio_specs array: One entry per scene with layered audio design
   - Include ambient sounds, specific sound effects, and human speech
   - Layer multiple audio elements: background ambience + specific sounds + dialogue
   - When humans are present, include natural speech that enhances the scene
   - Describe audio timing and emotional tone without technical specifications

Generate 10 complete video concepts following this EXACT level of detail for every scene. Each concept should feel like a premium commercial that could air during the Super Bowl.`;

    // Make request to OpenRouter
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Video Science',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        stream: true,
        temperature: 0.8 // Add some creativity while maintaining structure
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', {
        status: openRouterResponse.status,
        statusText: openRouterResponse.statusText,
        error: errorData
      });

      return NextResponse.json({
        message: 'Failed to get response from AI service.',
        debug: process.env.NODE_ENV === 'development' ? {
          status: openRouterResponse.status,
          error: errorData
        } : undefined
      }, { status: 500 });
    }

    // Create a readable stream to handle the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        const decoder = new TextDecoder();
        let totalContent = '';

        if (!reader) {
          console.error('No reader available from OpenRouter response');
          controller.close();
          return;
        }

        try {
          console.log('Starting stream processing for video concepts...');
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              console.log('Stream completed. Total content length:', totalContent.length);
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  continue;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    totalContent += content;
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (parseError) {
                  // Skip invalid JSON chunks
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
          const errorMessage = `Stream error: ${error instanceof Error ? error.message : 'Unknown error'}`;
          controller.enqueue(new TextEncoder().encode(errorMessage));
          controller.error(error);
        } finally {
          console.log('Stream processing finished');
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in generate-video-concepts API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
