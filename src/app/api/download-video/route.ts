import { NextResponse } from 'next/server';

/**
 * API route to download videos from social media URLs
 * Uses RapidAPI's TikTok/Instagram/YouTube downloader services
 */
export async function POST(request: Request) {
  try {
    const { url, platform } = await request.json();

    if (!url) {
      return NextResponse.json({ message: 'Video URL is required.' }, { status: 400 });
    }

    // For now, we'll use a free service approach
    // In production, you might want to use paid services like RapidAPI for better reliability
    
    let downloadUrl: string | null = null;
    let videoBuffer: Buffer | null = null;

    try {
      if (platform === 'TikTok') {
        // Use TikTok video downloader API
        downloadUrl = await downloadTikTokVideo(url);
      } else if (platform === 'Instagram') {
        // Use Instagram video downloader API
        downloadUrl = await downloadInstagramVideo(url);
      } else if (platform === 'YouTube') {
        // Use YouTube video downloader API
        downloadUrl = await downloadYouTubeVideo(url);
      } else {
        return NextResponse.json({ 
          message: `Video downloading for ${platform} is not yet supported.` 
        }, { status: 400 });
      }

      if (!downloadUrl) {
        return NextResponse.json({ 
          message: `Failed to get download URL for ${platform} video.` 
        }, { status: 400 });
      }

      // Download the video file
      const videoResponse = await fetch(downloadUrl);
      if (!videoResponse.ok) {
        throw new Error(`Failed to download video: ${videoResponse.statusText}`);
      }

      const arrayBuffer = await videoResponse.arrayBuffer();
      videoBuffer = Buffer.from(arrayBuffer);

      // Check file size (limit to 20MB for Gemini compatibility)
      const maxSize = 20 * 1024 * 1024;
      if (videoBuffer.length > maxSize) {
        return NextResponse.json({ 
          message: `Video is too large (${(videoBuffer.length / 1024 / 1024).toFixed(1)}MB). Maximum size is 20MB.` 
        }, { status: 400 });
      }

      // Return the video buffer as base64
      const base64Video = videoBuffer.toString('base64');
      
      return NextResponse.json({
        success: true,
        videoData: base64Video,
        mimeType: 'video/mp4',
        size: videoBuffer.length,
        platform
      });

    } catch (downloadError) {
      console.error(`Error downloading ${platform} video:`, downloadError);
      return NextResponse.json({ 
        message: `Failed to download video from ${platform}. The video might be private, deleted, or the service is temporarily unavailable.`,
        suggestion: 'Please try uploading the video file directly instead.'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in download-video API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}

/**
 * Download TikTok video using a free API service
 */
async function downloadTikTokVideo(url: string): Promise<string | null> {
  try {
    // Try multiple TikTok downloader services for better reliability
    const services = [
      {
        name: 'tikwm',
        url: 'https://tikwm.com/api/',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `url=${encodeURIComponent(url)}&hd=1`,
        parseResponse: (data: any) => data.code === 0 && data.data ? (data.data.play || data.data.wmplay) : null
      },
      {
        name: 'ssstik',
        url: 'https://ssstik.io/abc',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `id=${encodeURIComponent(url)}&locale=en&tt=Q2F0Y2g%3D`,
        parseResponse: (data: any) => {
          // This service returns HTML, would need parsing
          return null; // Simplified for now
        }
      }
    ];

    for (const service of services) {
      try {
        console.log(`Trying TikTok service: ${service.name}`);

        const response = await fetch(service.url, {
          method: service.method,
          headers: service.headers,
          body: service.body
        });

        if (!response.ok) {
          console.log(`${service.name} failed with status: ${response.status}`);
          continue;
        }

        const data = await response.json();
        const videoUrl = service.parseResponse(data);

        if (videoUrl) {
          console.log(`Successfully got video URL from ${service.name}`);
          return videoUrl;
        }
      } catch (serviceError) {
        console.log(`${service.name} service error:`, serviceError);
        continue;
      }
    }

    throw new Error('All TikTok download services failed');
  } catch (error) {
    console.error('TikTok download error:', error);
    return null;
  }
}

/**
 * Download Instagram video using a free API service
 */
async function downloadInstagramVideo(url: string): Promise<string | null> {
  try {
    // Using a free Instagram downloader API
    const apiUrl = 'https://api.instagram-downloader.com/download';
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url })
    });

    if (!response.ok) {
      throw new Error(`Instagram API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.success && data.data && data.data.video_url) {
      return data.data.video_url;
    }
    
    throw new Error('No video URL found in Instagram API response');
  } catch (error) {
    console.error('Instagram download error:', error);
    // Instagram is more restrictive, so this might fail often
    return null;
  }
}

/**
 * Download YouTube video using a free API service
 */
async function downloadYouTubeVideo(url: string): Promise<string | null> {
  try {
    // Extract video ID from YouTube URL
    const videoId = extractYouTubeVideoId(url);
    if (!videoId) {
      throw new Error('Invalid YouTube URL');
    }

    // Using a free YouTube downloader API
    const apiUrl = `https://youtube-mp36.p.rapidapi.com/dl?id=${videoId}`;
    
    // Note: This requires a RapidAPI key for the free tier
    // For a completely free solution, you might need to use different services
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'X-RapidAPI-Key': process.env.RAPIDAPI_KEY || '',
        'X-RapidAPI-Host': 'youtube-mp36.p.rapidapi.com'
      }
    });

    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.status === 'ok' && data.link) {
      return data.link;
    }
    
    throw new Error('No video URL found in YouTube API response');
  } catch (error) {
    console.error('YouTube download error:', error);
    return null;
  }
}

/**
 * Extract YouTube video ID from various YouTube URL formats
 */
function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/v\/([^&\n?#]+)/,
    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}
