import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { productName, productDescription, targetAudience, callToAction, behavioralFocus, includePrincipleDetails } = await request.json();

    if (!productName || !productDescription || !targetAudience || !callToAction) {
      return NextResponse.json({ message: 'Missing required fields.' }, { status: 400 });
    }

    // Read the Nancy Harhut markdown file
    const markdownPath = path.join(process.cwd(), 'public', '<PERSON> - Using Behavioral Science In Marketing.md');
    let nancyHarhutContent = '';

    try {
      nancyHarhutContent = await fs.readFile(markdownPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read Nancy Harhut markdown file:', readError);
      return NextResponse.json({ message: 'Failed to load Nancy Harhut behavioral science content for analysis.' }, { status: 500 });
    }

    const prompt = `You are a world-class behavioral science expert and marketing strategist, deeply versed in <PERSON>'s principles of using behavioral science in marketing. Your mission is to create specific, actionable behavioral science insights for this product.

**Core Reference: Nancy Harhut's "Using Behavioral Science in Marketing" - Complete Book Content**
${nancyHarhutContent}

---

**Product Information:**
- **Product/Service:** "${productName}"
- **Description:** "${productDescription}"
- **Target Audience:** "${targetAudience}"
- **Primary Call to Action:** "${callToAction}"
${behavioralFocus ? `- **Focus Areas:** "${behavioralFocus}"` : ''}

---

**Your Task: Generate Targeted Behavioral Science Insights**

Using Nancy Harhut's comprehensive behavioral science framework from her book above, create 10 specific, actionable behavioral science insights tailored to this product. ${behavioralFocus ? `Focus particularly on the specified areas: ${behavioralFocus}` : 'Cover a diverse range of principles from across all 17 chapters.'}

${includePrincipleDetails ? 'Include detailed explanations of the psychological principles being applied, with research context and background.' : 'Focus on practical application and implementation rather than theoretical explanations.'}

**CRITICAL: Respond ONLY with valid JSON. No other text.**

The JSON structure must be:
{
  "behavioral_insights": [
    {
      "title": "Clear, compelling title for the insight",
      "principle": "The specific Nancy Harhut behavioral science principle being applied (e.g., 'Loss Aversion', 'Social Proof', 'Scarcity Principle', etc.)",
      "application": "How this principle specifically applies to the product and target audience",
      "implementation": "Concrete, step-by-step implementation guide with specific tactics",
      "examples": ["Specific example 1", "Specific example 2", "Specific example 3"]
    }
  ]
}

**Requirements for each insight:**
1. **Title**: Should be compelling and specific to the product (e.g., "Leverage Loss Aversion to Drive FitTracker Pro Subscriptions")
2. **Principle**: Reference the exact Nancy Harhut principle from the book content
3. **Application**: Explain how this principle specifically applies to this product, audience, and business model
4. **Implementation**: Provide concrete, actionable steps that can be implemented immediately
5. **Examples**: Give 3 specific, realistic examples of how to apply this insight

**Focus Areas to Consider** (select the most relevant based on the product and ${behavioralFocus ? 'specified focus areas' : 'comprehensive coverage'}):
- Emotional and Rational Decision Making
- Loss Aversion and Endowment Effect
- Scarcity Principle (Urgency and Exclusivity)
- Reciprocity Principle (Give to Get)
- Social Proof (People Like Us)
- Storytelling and Consumer Engagement
- Autonomy Bias (Need for Control)
- Consistency Principle and Zeigarnik Effect
- Information Gap Theory (Curiosity)
- Authority Principle
- Choice Architecture and Status Quo Bias
- Labeling and Framing
- Automatic Compliance Triggers
- Language and Copy Optimization
- Availability Bias
- Von Restorff Effect (Standing Out)
- Temporal Landmarks and Discounting

**Quality Standards:**
- Each insight must be immediately actionable
- Examples must be specific to the product and realistic to implement
- Focus on ethical application of behavioral science
- Ensure insights are complementary and can work together
- Balance psychological effectiveness with genuine customer value

Generate exactly 10 behavioral science insights that will transform this product's marketing effectiveness using Nancy Harhut's proven framework.`;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Nancy Harhut Behavioral Science Playbook',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        stream: true,
        temperature: 0.7
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json({ message: 'Failed to get response from AI service.' }, { status: 500 });
    }

    // Create a readable stream to process the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  controller.close();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (e) {
                  // Skip invalid JSON
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in Nancy Harhut 2 analysis:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
