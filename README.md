# Next.js i18n Dashboard Starter

![Localizely progress](https://img.shields.io/localizely/progress/314cdb30-1e80-4acf-afbf-6195d164ca77?token=9b2c2727a3334e25952ea204a6cc8708c8465e4ae300425caff926fbc627ada7&languageCode=en) ![Localizely progress](https://img.shields.io/localizely/progress/314cdb30-1e80-4acf-afbf-6195d164ca77?token=9b2c2727a3334e25952ea204a6cc8708c8465e4ae300425caff926fbc627ada7&languageCode=de) ![Localizely progress](https://img.shields.io/localizely/progress/314cdb30-1e80-4acf-afbf-6195d164ca77?token=9b2c2727a3334e25952ea204a6cc8708c8465e4ae300425caff926fbc627ada7&languageCode=fr)

A dashboard template built with Next.js, Tailwind CSS, and React Intl. This template not only features internationalized routing but also showcases an array of localization features, making it an ideal starting point for building globally-friendly dashboard applications.

![demo](/public/demo.png)

## Demo

View a working [demo](https://nextjs-i18n-dashboard-template.vercel.app/).

## Getting Started

Run this Next.js app on your local machine by following the instructions below:

```bash
# Clone the repository from GitHub
git clone https://github.com/localizely/nextjs-i18n-dashboard-template.git

# Navigate into the cloned repository
cd nextjs-i18n-dashboard-template

# Install project dependencies
npm install

# Start the development server
npm run dev
```

## Deploy on Vercel

Deploy this ready-to-use template on Vercel by clicking the button below.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Flzoran%2Fnextjs-i18n-dashboard-template)

## Translation

Translating localization files can become increasingly complex, especially as your project expands. [**Localizely**](https://localizely.com/) simplifies the translation process, tracks progress, and seamlessly integrates localization into your workflow. It offers a variety of integrations, including [GitHub](https://localizely.com/github-integration/), [GitLab](https://localizely.com/gitlab-integration/), [Bitbucket](https://localizely.com/bitbucket-integration/), and [Figma](https://localizely.com/figma-integration/), along with powerful features such as [Reports and Statistics](https://localizely.com/reports-and-statistics/), [Machine Translation](https://localizely.com/machine-translation/), [Translation Memory](https://localizely.com/translation-memory/), and a [Glossary](https://localizely.com/project-glossary/).

To simplify the translation process, navigate to [Localizely](https://app.localizely.com/register) and create a new project. Afterwards, update the `localizely.yml` file with the missing data (e.g., project_id) and integrate localization into your workflow in a manner that best suits your needs, whether that's by using the [Localizely CLI](https://localizely.com/cli/) tool or any of the available integrations.

## Contribution

Feel free to share your ideas and report any issues on the template's [GitHub repo](https://github.com/lzoran/nextjs-i18n-dashboard-template/issues).

## Useful links

- [Next.js documentation](https://nextjs.org/docs)
- [Next.js deployment documentation](https://nextjs.org/docs/deployment)
- [Next.js internationalization (i18n) tutorial](https://localizely.com/blog/nextjs-i18n-tutorial/?tab=app-router)
- [React Intl documentation](https://formatjs.io/docs/react-intl/)
